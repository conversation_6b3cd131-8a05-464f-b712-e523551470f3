import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  Icon,
  Loading,
  CollapsibleCard,
  FormMultiWrapper,
  IconCard,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';

import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  UpdateProductDto,
  ProductDto,
  ClassificationPriceDto,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { useProduct } from '../../hooks/useProductQuery';
import { WarehouseService } from '@/modules/business/services/warehouse.service';

import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
}

interface ProductEditFormProps {
  productId: number;
  onSubmit: (values: UpdateProductDto) => Promise<UpdateProductDto | ProductDto | ProductWithImagesResponse | ProductWithUploadUrlsResponse>;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Interface cho biến thể sản phẩm trong form
interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  priceDescription?: string; // Mô tả giá cho biến thể (khi typePrice = STRING_PRICE)
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  customFields: SelectedCustomField[];
}

/**
 * Form chỉnh sửa sản phẩm
 */
const ProductEditForm: React.FC<ProductEditFormProps> = ({ productId, onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);

  // Gọi API lấy chi tiết sản phẩm
  const { data: product, isLoading: isLoadingProduct, error: productError } = useProduct(productId);

  // Schema validation với kiểm tra điều kiện theo loại giá (sao chép từ ProductForm)
  const productSchema = z.object({
    name: z.string().min(1, 'Tên sản phẩm không được để trống'),
    typePrice: z.nativeEnum(PriceTypeEnum, {
      errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
    }),
    listPrice: z.union([z.string(), z.number()]).optional(),
    salePrice: z.union([z.string(), z.number()]).optional(),
    currency: z.string().optional(),
    priceDescription: z.string().optional(),
    description: z.string().optional(),
    tags: z.array(z.string()).optional(),
    shipmentConfig: z.object({
      lengthCm: z.union([z.string(), z.number()]).optional(),
      widthCm: z.union([z.string(), z.number()]).optional(),
      heightCm: z.union([z.string(), z.number()]).optional(),
      weightGram: z.union([z.string(), z.number()]).optional(),
    }).optional(),
    media: z.any().optional(),
    customFields: z.any().optional(),
    classifications: z.any().optional(), // Đổi tên từ variants thành classifications
  }).superRefine((data, ctx) => {
    // Kiểm tra giá phù hợp với loại giá
    if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
      // Kiểm tra listPrice
      if (!data.listPrice || data.listPrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá niêm yết',
          path: ['listPrice'],
        });
      } else {
        const listPrice = Number(data.listPrice);
        if (isNaN(listPrice) || listPrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá niêm yết phải là số >= 0',
            path: ['listPrice'],
          });
        }
      }

      // Kiểm tra salePrice
      if (!data.salePrice || data.salePrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá bán',
          path: ['salePrice'],
        });
      } else {
        const salePrice = Number(data.salePrice);
        if (isNaN(salePrice) || salePrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá bán phải là số >= 0',
            path: ['salePrice'],
          });
        }
      }

      // Kiểm tra currency
      if (!data.currency || data.currency.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng chọn đơn vị tiền tệ',
          path: ['currency'],
        });
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (data.listPrice && data.salePrice && data.listPrice !== '' && data.salePrice !== '') {
        const listPrice = Number(data.listPrice);
        const salePrice = Number(data.salePrice);

        if (!isNaN(listPrice) && !isNaN(salePrice) && listPrice > 0 && salePrice > 0) {
          if (listPrice <= salePrice) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá niêm yết phải lớn hơn giá bán',
              path: ['listPrice'],
            });
          }
        }
      }
    } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!data.priceDescription || !data.priceDescription.trim()) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập mô tả giá',
          path: ['priceDescription'],
        });
      }
    }
  });

  // State cho tags (khởi tạo từ product)
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [existingImages, setExistingImages] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho phân loại sản phẩm (đổi tên từ variants)
  const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Utility function để chuyển đổi URL thành File object
  const createFileFromUrl = useCallback(async (url: string, filename: string): Promise<File> => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new File([blob], filename, { type: blob.type });
    } catch (error) {
      console.error('Error creating file from URL:', error);
      // Tạo file giả nếu không thể fetch
      const blob = new Blob([''], { type: 'image/jpeg' });
      return new File([blob], filename, { type: 'image/jpeg' });
    }
  }, []);

  // Khởi tạo dữ liệu phụ khi có sản phẩm (custom fields, classifications, images)
  useEffect(() => {
    if (product) {
      console.log('🔄 [ProductEditForm] Initializing additional product data:', product);

      // Set tags
      setTempTags(product.tags || []);

      // Populate custom fields từ metadata.customFields
      if (product.metadata?.customFields && Array.isArray(product.metadata.customFields)) {
        const customFields: SelectedCustomField[] = product.metadata.customFields.map((field, index) => ({
          id: Date.now() + index, // ID tạm thời cho UI
          fieldId: field.id, // Sử dụng id từ API
          label: field.label || `Custom Field ${field.id}`,
          component: field.type || 'text', // Sử dụng type làm component
          type: field.type || 'text',
          required: field.required || false,
          configJson: field.configJson || {},
          value: field.value || { value: '' },
        }));
        setProductCustomFields(customFields);
      }

      // Populate classifications từ API response (nếu có)
      if (product.classifications && Array.isArray(product.classifications)) {
        const classifications: ProductVariant[] = product.classifications.map((classification, index) => ({
          id: Date.now() + index, // ID tạm thời cho UI
          name: classification.type || `Classification ${index + 1}`,
          priceDescription: classification.priceDescription || '', // Thêm mô tả giá cho biến thể từ API
          listPrice: classification.price?.listPrice || 0,
          salePrice: classification.price?.salePrice || 0,
          currency: classification.price?.currency || 'VND',
          customFields: classification.customFields?.map((field, fieldIndex) => ({
            id: Date.now() + index * 1000 + fieldIndex, // ID tạm thời
            fieldId: field.customFieldId,
            label: `Field ${field.customFieldId}`,
            component: 'text',
            type: 'text',
            required: false,
            configJson: {},
            value: field.value || { value: '' },
          })) || [],
        }));
        setProductClassifications(classifications);
      }

      // Load ảnh hiện tại từ API với cấu trúc mới
      if (product.images && Array.isArray(product.images) && product.images.length > 0) {
        const loadExistingImages = async () => {
          try {
            const imagePromises = product.images?.map(async (image, index) => {
              const filename = `existing-image-${index + 1}.jpg`;
              const file = await createFileFromUrl(image.url, filename);
              return {
                file,
                id: `existing-${image.key}`,
                preview: image.url, // Sử dụng URL gốc làm preview
              };
            });

            if (imagePromises) {
              const existingImageFiles = await Promise.all(imagePromises);
              setExistingImages(existingImageFiles);
            }
          } catch (error) {
            console.error('Error loading existing images:', error);
          }
        };

        loadExistingImages();
      }
    }
  }, [product, createFileFromUrl]);



  // Xử lý khi submit form (sao chép từ ProductForm và chuyển đổi cho edit)
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 ProductEditForm handleSubmit called with values:', values);

    if (!values.name || !values.typePrice) {
      console.error('❌ Missing required fields:', { name: values.name, typePrice: values.typePrice });
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', formValues);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        console.error('❌ Price validation error:', priceError);
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo image operations cho API
      const imageOperations: Array<{
        operation: 'ADD' | 'DELETE';
        position?: number;
        key?: string;
        mimeType?: string;
      }> = [];

      // Thêm operations cho ảnh mới (ADD)
      if (mediaFiles.length > 0) {
        mediaFiles.forEach(fileData => {
          imageOperations.push({
            operation: 'ADD',
            mimeType: fileData.file.type,
          });
        });
      }

      const productData: UpdateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: priceData,
        description: formValues.description || undefined,
        tags: tempTags && tempTags.length > 0 ? tempTags : undefined,
        shipmentConfig: getShipmentConfig(formValues),
        // Thêm image operations nếu có
        images: imageOperations.length > 0 ? imageOperations : undefined,
        // Thêm custom fields cho sản phẩm với cấu trúc đúng theo API
        customFields: productCustomFields.length > 0 ? productCustomFields.map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value.value,
          },
        })) : undefined,
        // Chỉ gửi classifications với custom fields (nếu có)
        classifications: productClassifications.length > 0 ? productClassifications.map(variant => ({
          type: variant.name,
          price: {
            listPrice: Number(variant.listPrice) || 0,
            salePrice: Number(variant.salePrice) || 0,
            currency: variant.currency,
            ...(variant.priceDescription && { priceDescription: variant.priceDescription }), // Thêm mô tả giá vào trong price object
          },
          customFields: variant.customFields.map(field => ({
            customFieldId: field.fieldId,
            value: {
              value: field.value.value,
            },
          })),
        })) : undefined,
      };

      console.log('📤 Final product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      console.log('✅ Product updated successfully:', response);

      // Upload media nếu có và API trả về uploadUrls
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls = response &&
                               typeof response === 'object' &&
                               'uploadUrls' in response &&
                               response.uploadUrls &&
                               typeof response.uploadUrls === 'object' &&
                               'imagesUploadUrls' in response.uploadUrls &&
                               Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              console.log('🚀 Starting image upload with TaskQueue...');
              console.log('📁 Media files:', mediaFiles.length);
              console.log('🔗 Upload URLs from backend:', uploadUrls.length);
              console.log('📋 Backend response uploadUrls:', uploadUrls);

              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, { skipCacheInvalidation: true });

              console.log('✅ All product images uploaded successfully');

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t('business:product.mediaUploadSuccess', 'Tải lên ảnh sản phẩm thành công'),
                duration: 3000,
              });
            } else {
              console.warn('⚠️ Upload URLs array is empty');
              NotificationUtil.warning({
                message: t('business:product.mediaUploadWarning', 'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'),
                duration: 5000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');

            NotificationUtil.warning({
              message: t('business:product.mediaUploadWarning', 'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading product images:', uploadError);
          NotificationUtil.warning({
            message: t('business:product.mediaUploadError', 'Có lỗi xảy ra khi tải lên ảnh sản phẩm'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in ProductEditForm handleSubmit:', error);
      setIsUploading(false);

      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        console.error('Validation errors:', error);
        NotificationUtil.error({
          message: t('business:product.validationError'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.updateError'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá (sao chép từ ProductForm)
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      // Kiểm tra đầy đủ các trường bắt buộc
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }

      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }

      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }

      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }

      return {
        priceDescription: values.priceDescription.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.NO_PRICE) {
      return null;
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Hàm lấy dữ liệu cấu hình vận chuyển (sao chép từ ProductForm)
  const getShipmentConfig = (values: ProductFormValues) => {
    if (!values.shipmentConfig) return undefined;

    const config = values.shipmentConfig;
    const hasAnyValue = config.lengthCm || config.widthCm || config.heightCm || config.weightGram;

    if (!hasAnyValue) return undefined;

    return {
      lengthCm: Number(config.lengthCm) || undefined,
      widthCm: Number(config.widthCm) || undefined,
      heightCm: Number(config.heightCm) || undefined,
      weightGram: Number(config.weightGram) || undefined,
    };
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính (sao chép từ ProductForm)
  const handleToggleCustomFieldToProduct = useCallback((fieldId: number, fieldData?: Record<string, unknown>) => {
    setProductCustomFields(prev => {
      // Kiểm tra xem trường đã tồn tại chưa
      const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

      if (existingFieldIndex !== -1) {
        // Nếu đã tồn tại, xóa nó (bỏ chọn)
        return prev.filter((_, index) => index !== existingFieldIndex);
      }

      // Thêm trường mới với thông tin đầy đủ
      const newField: SelectedCustomField = {
        id: Date.now(), // ID tạm thời
        fieldId,
        label: (fieldData?.label as string) || `Field ${fieldId}`,
        component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
        type: (fieldData?.type as string) || 'text',
        required: (fieldData?.required as boolean) || false,
        configJson: (fieldData?.configJson as Record<string, unknown>) || {},
        value: { value: '' }, // Giá trị mặc định
      };

      return [...prev, newField];
    });
  }, []);

  // Xóa trường tùy chỉnh khỏi sản phẩm chính (sao chép từ ProductForm)
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Thêm/xóa trường tùy chỉnh vào phân loại (sao chép từ ProductForm)
  const handleToggleCustomFieldToVariant = useCallback((variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        // Kiểm tra xem trường đã tồn tại trong phân loại chưa
        const existingFieldIndex = variant.customFields.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          // Nếu đã tồn tại, xóa nó (bỏ chọn)
          return {
            ...variant,
            customFields: variant.customFields.filter((_, index) => index !== existingFieldIndex)
          };
        }

        // Thêm trường mới vào phân loại với thông tin đầy đủ
        return {
          ...variant,
          customFields: [
            ...variant.customFields,
            {
              id: Date.now(), // ID tạm thời
              fieldId,
              label: (fieldData?.label as string) || `Field ${fieldId}`,
              component: (fieldData?.component as string) || 'text',
              type: (fieldData?.type as string) || 'string',
              required: (fieldData?.required as boolean) || false,
              configJson: (fieldData?.configJson as Record<string, unknown>) || {},
              value: { value: '' }, // Giá trị mặc định
            }
          ]
        };
      }
      return variant;
    }));
  }, []);

  // Xóa trường tùy chỉnh khỏi phân loại (sao chép từ ProductForm)
  const handleRemoveCustomFieldFromVariant = useCallback((variantId: number, customFieldId: number) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.filter(field => field.id !== customFieldId)
        };
      }
      return variant;
    }));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong phân loại (sao chép từ ProductForm)
  const handleUpdateCustomFieldInVariant = useCallback((variantId: number, customFieldId: number, value: string) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          customFields: variant.customFields.map(field => {
            if (field.id === customFieldId) {
              return {
                ...field,
                value: { value }
              };
            }
            return field;
          })
        };
      }
      return variant;
    }));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính (sao chép từ ProductForm)
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev => prev.map(field => {
      if (field.id === customFieldId) {
        return {
          ...field,
          value: { value }
        };
      }
      return field;
    }));
  }, []);

  // Thêm phân loại mới (sao chép từ ProductForm)
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(),
      name: '',
      priceDescription: '', // Mô tả giá cho biến thể
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      customFields: [],
    };
    setProductClassifications(prev => [...prev, newVariant]);
  }, []);

  // Xóa phân loại (sao chép từ ProductForm)
  const handleRemoveVariant = useCallback((variantId: number) => {
    setProductClassifications(prev => prev.filter(variant => variant.id !== variantId));
  }, []);

  // Cập nhật thông tin phân loại (sao chép từ ProductForm)
  const handleUpdateVariant = useCallback((variantId: number, field: keyof ProductVariant, value: string) => {
    setProductClassifications(prev => prev.map(variant => {
      if (variant.id === variantId) {
        return {
          ...variant,
          [field]: value
        };
      }
      return variant;
    }));
  }, []);

  // Giá trị mặc định cho form - dynamic dựa trên dữ liệu sản phẩm
  const defaultValues = useMemo(() => {
    if (!product) {
      // Trả về giá trị rỗng khi chưa có dữ liệu
      return {
        name: '',
        typePrice: PriceTypeEnum.HAS_PRICE,
        listPrice: '',
        salePrice: '',
        currency: 'VND',
        priceDescription: '',
        description: '',
        tags: [],
        shipmentConfig: {
          lengthCm: '',
          widthCm: '',
          heightCm: '',
          weightGram: '',
        },
        customFields: [],
        media: [],
        classifications: [],
      };
    }

    // Tạo defaultValues từ dữ liệu sản phẩm
    const formData: Record<string, unknown> = {
      name: product.name,
      typePrice: product.typePrice,
      description: product.description || '',
      tags: product.tags || [],
    };

    // Xử lý giá dựa trên loại giá và cấu trúc API mới
    if (product.typePrice === PriceTypeEnum.HAS_PRICE && product.price) {
      const price = product.price as HasPriceDto;
      formData.listPrice = price.listPrice;
      formData.salePrice = price.salePrice;
      formData.currency = price.currency;
    } else if (product.typePrice === PriceTypeEnum.STRING_PRICE && product.price) {
      const price = product.price as StringPriceDto;
      formData.priceDescription = price.priceDescription;
    } else if (product.typePrice === PriceTypeEnum.NO_PRICE) {
      // Không có giá - để trống các trường giá
      formData.listPrice = '';
      formData.salePrice = '';
      formData.currency = 'VND';
      formData.priceDescription = '';
    }

    // Xử lý shipment config
    if (product.shipmentConfig) {
      formData.shipmentConfig = {
        lengthCm: product.shipmentConfig.lengthCm || '',
        widthCm: product.shipmentConfig.widthCm || '',
        heightCm: product.shipmentConfig.heightCm || '',
        weightGram: product.shipmentConfig.weightGram || '',
      };
    } else {
      formData.shipmentConfig = {
        lengthCm: '',
        widthCm: '',
        heightCm: '',
        weightGram: '',
      };
    }

    formData.customFields = [];
    formData.media = [];
    formData.classifications = [];

    return formData;
  }, [product]);

  // Hiển thị loading khi đang fetch chi tiết sản phẩm
  if (isLoadingProduct) {
    return (
      <Card title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Loading />
        </div>
      </Card>
    );
  }

  // Không render form nếu chưa có dữ liệu sản phẩm
  // Điều này đảm bảo form chỉ được render khi đã có đầy đủ dữ liệu
  if (!product) {
    return (
      <Card title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Loading />
        </div>
      </Card>
    );
  }

  // Hiển thị lỗi nếu có lỗi khi load sản phẩm
  if (productError) {
    return (
      <Card title={t('business:product.form.editTitle')}>
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <Icon name="alert-circle" size="lg" className="text-red-500 mb-4" />
          <Typography variant="h6" className="text-red-600 mb-2">
            {t('business:product.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 mb-4">
            {productError?.message || t('business:product.loadErrorDetail', 'Vui lòng thử lại sau')}
          </Typography>
          <Button variant="outline" onClick={onCancel}>
            {t('common:close')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <FormMultiWrapper title={t('business:product.form.editTitle')}>
      <Form
        key={product?.id || 'empty'} // Force re-render khi product thay đổi
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        onError={(errors) => {
          console.error('🔥 Form validation errors:', errors);

          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            console.error(`❌ Field "${field}":`, errors[field]);
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
            if (errors[field] && typeof errors[field] === 'object' && 'type' in errors[field]) {
              console.error(`   Type: ${(errors[field] as { type: string }).type}`);
            }
          });

          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.general', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.descriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();

                          // Lấy tag mới
                          const newTag = e.currentTarget.value.trim();

                          // Thêm tag mới nếu chưa tồn tại
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags); // Đồng bộ với form
                          }

                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags); // Đồng bộ với form
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                  { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
                ]}
              />
            </FormItem>

            {/* Hiển thị các trường giá dựa trên loại giá */}
            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                  <Input fullWidth type="number" min="0" placeholder="Nhập giá niêm yết" />
                </FormItem>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder="Nhập giá bán" />
                </FormItem>
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Select
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={value => field.onChange(value)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    )}
                  />
                </FormItem>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Hình ảnh sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.images', '3. Hình ảnh sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <FormItem name="media" label={t('business:product.form.media')}>
            <MultiFileUpload
              mediaOnly={true}
              accept="image/*,video/*"
              placeholder={t(
                'business:product.form.mediaPlaceholder',
                'Kéo thả hoặc click để tải lên ảnh/video'
              )}
              onChange={files => {
                // Tách ảnh hiện tại và ảnh mới
                const existingImageIds = existingImages.map(img => img.id);
                const updatedExistingImages = files.filter(file => existingImageIds.includes(file.id));
                const newMediaFiles = files.filter(file => !existingImageIds.includes(file.id));

                setExistingImages(updatedExistingImages);
                setMediaFiles(newMediaFiles);
                // Không gọi setValue để tránh reset form
              }}
              value={[...existingImages, ...mediaFiles]}
            />
          </FormItem>
        </CollapsibleCard>

        {/* 4. Quản lý tồn kho */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.inventory', '4. Quản lý tồn kho')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="inventory.sku"
                label={t('business:product.form.inventory.sku', 'SKU')}
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.inventory.skuPlaceholder', 'Nhập mã SKU')}
                />
              </FormItem>

              <FormItem
                name="inventory.barcode"
                label={t('business:product.form.inventory.barcode', 'Barcode')}
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.inventory.barcodePlaceholder', 'Nhập mã vạch')}
                />
              </FormItem>

              <FormItem
                name="inventory.quantity"
                label={t('business:product.form.inventory.quantity', 'Số lượng trong kho')}
              >
                <Input
                  fullWidth
                  type="number"
                  min="0"
                  placeholder="0"
                />
              </FormItem>

              <FormItem
                name="inventory.warehouseId"
                label={t('business:product.form.inventory.warehouse', 'Kho')}
              >
                <AsyncSelectWithPagination
                  fullWidth
                  placeholder={t('business:product.form.inventory.warehousePlaceholder', 'Chọn kho')}
                  loadOptions={async ({ search, page, limit }: {
                    search?: string;
                    page?: number;
                    limit?: number;
                  }) => {
                    // Gọi API thực tế để lấy danh sách kho
                    const response = await WarehouseService.getWarehousesForSelect({
                      search,
                      page: page || 1,
                      limit: limit || 20,
                    });

                    return response;
                  }}
                  searchOnEnter={true}
                />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 5. Vận chuyển */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.shipping', '5. Vận chuyển')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="shipmentConfig.widthCm"
                label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.heightCm"
                label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.lengthCm"
                label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.weightGram"
                label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 6. Mẫu mã */}
        <CollapsibleCard
          title={
            <div className="flex items-center justify-between w-full">
              <Typography variant="h6" className="font-medium">
                {t('business:product.form.sections.variants', '6. Mẫu mã')}
              </Typography>
              <div
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation(); // Ngăn không cho toggle card
                  handleAddVariant();
                }}
                className="cursor-pointer"
              >
                <IconCard
                  icon="plus"
                  variant="primary"
                  size="sm"
                  title={t('business:product.form.variants.addVariant', 'Thêm phân loại')}
                />
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-4"
        >
          {/* Danh sách phân loại (đổi tên từ biến thể) */}
          {productClassifications.length > 0 ? (
            <div className="space-y-4">
              {productClassifications.map((variant, index) => (
                <CollapsibleCard
                  key={variant.id}
                  title={
                    <div className="flex justify-between items-center w-full">
                      <div className="flex items-center space-x-4">
                        <Typography variant="body2" className="font-medium">
                          {variant.name || `${t('business:product.form.variants.variant', 'Phân loại')} ${index + 1}`}
                        </Typography>
                        {variant.listPrice && variant.salePrice && (
                          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                            {Number(variant.salePrice) > 0 ? `${Number(variant.salePrice).toLocaleString()} ${variant.currency}` : '0 VND'}
                          </Typography>
                        )}
                        {variant.id && (
                          <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                            ID: {variant.id.toString().slice(-4)}
                          </Typography>
                        )}
                      </div>
                      <div
                        onClick={e => {
                          e.stopPropagation();
                          handleRemoveVariant(variant.id);
                        }}
                        className="cursor-pointer"
                      >
                        <IconCard
                          icon="trash"
                          variant="danger"
                          size="sm"
                          title={t('common:delete', 'Xóa')}
                        />
                      </div>
                    </div>
                  }
                  defaultOpen={true}
                >

                  <div className="space-y-4 mb-4">
                    {/* Tên và mô tả biến thể */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label={t('business:product.form.variants.name', 'Tên biến thể')}>
                        <Input
                          fullWidth
                          value={variant.name}
                          onChange={e => handleUpdateVariant(variant.id, 'name', e.target.value)}
                          placeholder={t(
                            'business:product.form.variants.namePlaceholder',
                            'Nhập tên biến thể'
                          )}
                        />
                      </FormItem>

                      <FormItem label={t('business:product.form.variants.priceDescription', 'Mô tả giá')}>
                        <Textarea
                          fullWidth
                          rows={3}
                          value={variant.priceDescription || ''}
                          onChange={e => handleUpdateVariant(variant.id, 'priceDescription', e.target.value)}
                          placeholder={t(
                            'business:product.form.variants.priceDescriptionPlaceholder',
                            'Nhập mô tả giá cho biến thể này'
                          )}
                        />
                      </FormItem>
                    </div>

                    {/* Các trường giá - chỉ hiển thị khi typePrice = HAS_PRICE */}
                    <ConditionalField
                      condition={{
                        field: 'typePrice',
                        type: ConditionType.EQUALS,
                        value: PriceTypeEnum.HAS_PRICE,
                      }}
                    >
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormItem
                          label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                        >
                          <Select
                            fullWidth
                            value={variant.currency}
                            onChange={val => handleUpdateVariant(variant.id, 'currency', val as string)}
                            options={[
                              { value: 'VND', label: 'VND' },
                              { value: 'USD', label: 'USD' },
                              { value: 'EUR', label: 'EUR' },
                            ]}
                          />
                        </FormItem>

                        <FormItem label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}>
                          <Input
                            fullWidth
                            type="number"
                            min="0"
                            value={variant.listPrice}
                            onChange={e => handleUpdateVariant(variant.id, 'listPrice', e.target.value)}
                            placeholder="0"
                          />
                        </FormItem>

                        <FormItem label={t('business:product.form.variants.salePrice', 'Giá bán')}>
                          <Input
                            fullWidth
                            type="number"
                            min="0"
                            value={variant.salePrice}
                            onChange={e => handleUpdateVariant(variant.id, 'salePrice', e.target.value)}
                            placeholder="0"
                          />
                        </FormItem>
                      </div>
                    </ConditionalField>
                  </div>

                  {/* Trường tùy chỉnh cho biến thể */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="subtitle2">
                        {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                      </Typography>
                    </div>

                    <SimpleCustomFieldSelector
                      onFieldSelect={fieldData => {
                        handleToggleCustomFieldToVariant(
                          variant.id,
                          fieldData.id,
                          fieldData as unknown as Record<string, unknown>
                        );
                      }}
                      selectedFieldIds={variant.customFields.map(f => f.fieldId)}
                      placeholder={t(
                        'business:product.form.variants.searchCustomField',
                        'Nhập từ khóa và nhấn Enter để tìm thuộc tính...'
                      )}
                    />

                    {variant.customFields.length > 0 && (
                      <div className="space-y-3">
                        {variant.customFields.map(field => (
                          <CustomFieldRenderer
                            key={field.id}
                            field={field}
                            value={(field.value.value as string) || ''}
                            onChange={value =>
                              handleUpdateCustomFieldInVariant(
                                variant.id,
                                field.id,
                                value as string
                              )
                            }
                            onRemove={() =>
                              handleRemoveCustomFieldFromVariant(variant.id, field.id)
                            }
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </CollapsibleCard>
              ))}
            </div>
          ) : (
            <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                {t(
                  'business:product.form.variants.noVariants',
                  'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.'
                )}
              </Typography>
            </div>
          )}
        </CollapsibleCard>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(f => f.fieldId)}
              placeholder={t(
                'business:product.form.customFields.searchPlaceholder',
                'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
              )}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-3">
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={(field.value.value as string) || ''}
                    onChange={value => handleUpdateCustomFieldInProduct(field.id, value as string)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        <div className="flex flex-row justify-end gap-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            type="button"
            disabled={isSubmitting || isUploading}
            className="w-full sm:w-auto order-3 sm:order-1"
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isUploading}
            className="w-full sm:w-auto order-1 sm:order-3"
          >
            {isUploading ? t('business:product.uploading') : t('common:save')}
          </Button>
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ProductEditForm;
